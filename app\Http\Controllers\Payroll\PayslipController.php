<?php

namespace App\Http\Controllers\Payroll;

use App\Http\Controllers\Controller;
use App\Http\Requests\V1\StorePayslipRequest;
use App\Services\V1\Payroll\PayslipService;
use App\Http\Resources\V1\Payroll\Payslip\EmployeesCollection;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use RuntimeException;

/**
 * PayslipController - Handles payslip generation and download requests
 * 
 * @version 2.0
 */
class PayslipController extends Controller
{
    public function __construct(private PayslipService $payslipService)
    {
    }

    /**
     * Generate payslips and store as ZIP file
     */
    public function generate(Request $request): JsonResponse
    {
        try {
            // Validate request data
            $validated = $request->validate([
                'end_date' => 'required|date',
                'employee_ids' => 'sometimes|array',
                'employee_ids.*' => 'integer|min:1',
            ]);

            $validated['lang'] = $request->header('Accept-Language');

            Log::info('Starting payslip generation and storage', [
                'request_data' => $validated,
                'user_id' => auth()->id()
            ]);

            // Generate and store payslips ZIP
            $result = $this->payslipService->generateAndStorePayslips($validated);

            // Create public URL for the stored file
            $publicUrl = url('storage/' . str_replace('storage/app/public/', '', $result['path']));

            Log::info('Payslip generation and storage completed successfully', [
                'file_path' => $result['path'],
                'total_employees' => $result['total_employees'],
                'public_url' => $publicUrl,
                'user_id' => auth()->id()
            ]);

            return getResponseStructure([
                'file_path' => $result['path'],
                'public_url' => $publicUrl,
                'total_employees' => $result['total_employees'],
                'filename' => basename($result['path'])
            ], HttpStatusCodeUtil::OK, 'Payslips generated and stored successfully');

        } catch (ValidationException $e) {
            Log::warning('Validation failed for payslip generation', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            return getResponseStructure([
                'errors' => $e->errors()
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'Invalid request data');

        } catch (RuntimeException $e) {
            Log::error('Runtime error in payslip generation', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return getResponseStructure([
                'errors' => ['message' => $e->getMessage()]
            ], HttpStatusCodeUtil::BAD_REQUEST, 'Payslip generation failed');

        } catch (\Exception $e) {
            Log::error('Payslip generation failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return getResponseStructure([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, 'Failed to generate payslips');
        }
    }

    /**
     * Download stored payslip ZIP file
     */
    public function downloadStored(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        try {
            $validated = $request->validate([
                'file_path' => 'required|string'
            ]);

            $filePath = $validated['file_path'];

            // Ensure the file exists and is within allowed storage path
            if (!file_exists($filePath) || !str_starts_with($filePath, storage_path('app/public/companies/'))) {
                return response()->json([
                    'errors' => ['message' => 'File not found or access denied']
                ], HttpStatusCodeUtil::NOT_FOUND);
            }

            Log::info('Downloading stored payslip file', [
                'file_path' => $filePath,
                'user_id' => auth()->id()
            ]);

            return response()->download($filePath, basename($filePath), [
                'Content-Type' => 'application/zip',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'errors' => $e->errors(),
                'message' => 'Invalid request data'
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY);

        } catch (\Exception $e) {
            Log::error('Failed to download stored payslip file', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Download payslips directly (without saving to server)
     */
    public function download(StorePayslipRequest $request): \Symfony\Component\HttpFoundation\Response
    {
        try {
            $data = $request->validated();

            Log::info('Starting direct payslip download', [
                'request_data' => $data,
                'user_id' => auth()->id()
            ]);

            $result = $this->payslipService->generatePayslipsZipForDownload([
                'end_date' => $data['end_date'],
                'employee_ids' => $data['employee_ids'] ?? null,
                'lang' => $data['lang'] ?? 'en'
            ]);

            Log::info('Direct payslip download completed', [
                'filename' => $result['filename'],
                'total_employees' => $result['total_employees'],
                'successfully_downloaded' => $result['successfully_downloaded'],
                'zip_size' => strlen($result['content']) . ' bytes',
                'user_id' => auth()->id()
            ]);

            // Return the ZIP file as a download response
            return response($result['content'])
                ->header('Content-Type', $result['mime_type'])
                ->header('Content-Disposition', 'attachment; filename="' . $result['filename'] . '"')
                ->header('Content-Length', strlen($result['content']))
                ->header('X-Total-Employees', $result['total_employees'])
                ->header('X-Successfully-Downloaded', $result['successfully_downloaded'])
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (ValidationException $e) {
            Log::warning('Validation failed for direct payslip download', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'errors' => $e->errors(),
                'message' => 'Invalid request data'
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY);

        } catch (RuntimeException $e) {
            Log::error('Runtime error in direct payslip download', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'errors' => ['message' => $e->getMessage()]
            ], HttpStatusCodeUtil::BAD_REQUEST);

        } catch (\Exception $e) {
            Log::error('Direct payslip download failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * List stored payslip files for a company
     */
    public function listStored(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'company_id' => 'sometimes|integer',
                'year' => 'sometimes|integer|min:2020|max:' . (date('Y') + 1),
                'month' => 'sometimes|integer|min:1|max:12'
            ]);

            $companyId = $validated['company_id'] ?? null;
            $year = $validated['year'] ?? date('Y');
            $month = $validated['month'] ?? null;

            Log::info('Listing stored payslip files', [
                'company_id' => $companyId,
                'year' => $year,
                'month' => $month,
                'user_id' => auth()->id()
            ]);

            $files = $this->payslipService->listStoredFiles($companyId, $year, $month);

            return getResponseStructure([
                'files' => $files,
                'total_files' => count($files),
                'filters' => [
                    'company_id' => $companyId,
                    'year' => $year,
                    'month' => $month
                ]
            ], HttpStatusCodeUtil::OK, 'Stored payslip files retrieved successfully');

        } catch (ValidationException $e) {
            return getResponseStructure([
                'errors' => $e->errors()
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'Invalid request parameters');

        } catch (\Exception $e) {
            Log::error('Failed to list stored payslip files', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return getResponseStructure([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, 'Failed to retrieve stored files');
        }
    }

    /**
     * Get employees list with filters for payslip management
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getEmployees(Request $request): JsonResponse
    {
        try {
            // Validate required data
            $validated = $request->validate([
                'end_date' => 'required|date',
                // Filter parameters
                'branches' => 'sometimes|array',
                'branches.*' => 'integer|min:1',
                'departments' => 'sometimes|array',
                'departments.*' => 'integer|min:1',
                'cash_only' => 'sometimes|boolean',
                'is_downloaded' => 'sometimes|boolean',
                'search' => 'sometimes|string|max:255'
            ]);

            // Prepare data for service (company_id resolved automatically)
            $data = [
                'end_date' => $validated['end_date']
            ];

            // Prepare filters
            $filters = [];
            if (isset($validated['branches'])) {
                $filters['branches'] = $validated['branches'];
            }
            if (isset($validated['departments'])) {
                $filters['departments'] = $validated['departments'];
            }
            if (isset($validated['cash_only'])) {
                $filters['cash_only'] = $validated['cash_only'];
            }
            if (isset($validated['is_downloaded'])) {
                $filters['is_downloaded'] = $validated['is_downloaded'];
            }
            if (isset($validated['search'])) {
                $filters['search'] = $validated['search'];
            }

            // Get employees from service
            $employees = $this->payslipService->getEmployees($data, $filters);

            Log::info('Employees retrieved successfully', [
                'total_employees' => $employees->count(),
                'filters_applied' => array_keys($filters),
                'user_id' => auth()->id()
            ]);

            // Return using getResponseStructure with resource collection data
            $employeesCollection = new EmployeesCollection($employees);
            $collectionData = $employeesCollection->toResponse(request())->getData();

            return getResponseStructure([
                'data' => $collectionData->data,
            ], HttpStatusCodeUtil::OK, 'Employees retrieved successfully');

        } catch (ValidationException $e) {
            return getResponseStructure([
                'errors' => $e->errors()
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'Invalid request parameters');

        } catch (\Exception $e) {
            Log::error('Failed to get employees', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return getResponseStructure([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, 'Failed to retrieve employees');
        }
    }
}
