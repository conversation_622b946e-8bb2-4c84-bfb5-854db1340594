<?php

namespace App\Services\V1\Payroll;

use App\Repositories\NewCompanyRepository;
use App\Repositories\V1\Payroll\PayslipRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use ZipArchive;
use Exception;
use Illuminate\Support\Facades\File;

/**
 * PayslipService - Generate payslips and create ZIP for direct download
 */
class PayslipService
{
    private PayslipRepository $payslipRepository;
    private NewCompanyRepository $newCompanyRepository;
    private PayrollsRepository $payrollsRepository;
    private array $config;
    private int|null $companyId;

    public function __construct(
        PayslipRepository $payslipRepository,
        NewCompanyRepository $newCompanyRepository,
        PayrollsRepository $payrollsRepository
    ) {
        $this->payslipRepository = $payslipRepository;
        $this->newCompanyRepository = $newCompanyRepository;
        $this->payrollsRepository = $payrollsRepository;

        // Load configuration
        $this->config = config('payslips', [
            'default_language' => 'en',
            'supported_languages' => [
                'en' => ['direction' => 'ltr'],
                'ar' => ['direction' => 'rtl']
            ]
        ]);

        // Register Amiri font for Arabic support
        $this->registerAmiriFont();

        $this->companyId = null;
    }

    private function registerAmiriFont(): void
    {
        try {
            // Use the font files that are already processed by DomPDF
            $fontPath = config('payslips.storage.fonts_path');
            $regularFont = $fontPath . 'Amiri-Regular.ttf';
            $boldFont = $fontPath . 'Amiri-Bold.ttf';

            // Check if font files exist
            if (!file_exists($regularFont) || !file_exists($boldFont)) {
                Log::warning('Amiri font files not found', [
                    'regular' => $regularFont,
                    'bold' => $boldFont
                ]);
                return;
            }

            Log::info('Amiri fonts found and ready to use', [
                'regular_font' => $regularFont,
                'bold_font' => $boldFont
            ]);

        } catch (Exception $e) {
            Log::error('Failed to register Amiri fonts', [
                'error' => $e->getMessage()
            ]);
            // Don't throw exception, just log warning - system can still work without Arabic fonts
        }
    }

    /**
     * Main method: Generate and store payslips as ZIP
     */
    public function generateAndStorePayslips(array $data): array
    {
        try {
            // Prepare payroll data (this handles company_id resolution)
            $payrollData = $this->preparePayrollData($data);

            // Add employee_ids to payroll data if provided
            if (!empty($data['employee_ids'])) {
                $payrollData['employee_ids'] = $data['employee_ids'];
            }

            Log::info('Starting payslip generation', [
                'payroll_data' => $payrollData,
                'original_data' => $data
            ]);

            // 1. Get data
            $rawPayslips = $this->payslipRepository->getPayslips($payrollData);
            if ($rawPayslips->isEmpty()) {
                throw new Exception('No payslip data found');
            }

            $payslips = $this->processPayslipsData($rawPayslips);
            $company = $this->newCompanyRepository->getCompanyInfo($payrollData['company_id']);

            // 2. Create ZIP file path
            $zipInfo = $this->createZipPath($company, $payrollData['month'], $payrollData['year']);

            // 3. Generate PDFs and create ZIP
            $language = $data['lang'] ?? 'en';
            Log::info('Payslip generation language', [
                'requested_language' => $data['lang'] ?? 'not_set',
                'final_language' => $language,
                'data_keys' => array_keys($data)
            ]);

            $this->generatePDFsAndCreateZip($payslips, $zipInfo, $company->name, $language);

            Log::info('Payslip generation completed', [
                'total_employees' => $payslips->count(),
                'zip_path' => $zipInfo['full_path']
            ]);

            return [
                'path' => $zipInfo['full_path'],
                'total_employees' => $payslips->count()
            ];

        } catch (Exception $e) {
            Log::error('Payslip generation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Create ZIP file path structure
     */
    private function createZipPath(object $company, int $month, int $year): array
    {
        $companyCode = preg_replace('/[^a-zA-Z0-9_-]/', '_', $company->code);
        $baseDir = "companies/{$companyCode}/payslips";
        $fileName = sprintf('%02d_%d_payslips.zip', $month, $year);
        $fullPath = "{$baseDir}/{$fileName}";

        return [
            'base_dir' => $baseDir,
            'file_name' => $fileName,
            'full_path' => $fullPath,
            'absolute_path' => storage_path("app/public/{$fullPath}")
        ];
    }

    /**
     * Core method: Generate PDFs and create ZIP
     */
    private function generatePDFsAndCreateZip(Collection $payslips, array $zipInfo, string $companyName, string $language): void
    {
        // Ensure ZIP directory exists
        $zipDir = dirname($zipInfo['absolute_path']);
        if (!File::exists($zipDir)) {
            File::makeDirectory($zipDir, 0755, true);
        }

        // Remove existing ZIP file
        if (File::exists($zipInfo['absolute_path'])) {
            File::delete($zipInfo['absolute_path']);
        }

        // Create ZIP
        $zip = new ZipArchive();
        $result = $zip->open($zipInfo['absolute_path'], ZipArchive::CREATE);

        if ($result !== true) {
            throw new Exception("Cannot create ZIP file. Error code: {$result}");
        }

        try {
            // Generate PDF for each employee and add to ZIP
            foreach ($payslips as $payslip) {
                // 1. Generate PDF for employee
                $pdf = $this->generateEmployeePDF($payslip, $companyName, $language);

                // 2. Add PDF to ZIP
                $fileName = "payslip_{$payslip['employee']['code']}.pdf";
                $pdfContent = $pdf->output();

                $zip->addFromString($fileName, $pdfContent);

                Log::debug("Added PDF to ZIP: {$fileName}");
            }

            // 3. Store ZIP
            $zip->close();

            // Verify ZIP was created
            if (!File::exists($zipInfo['absolute_path'])) {
                throw new Exception('ZIP file was not created');
            }

            Log::info('ZIP file created successfully', [
                'path' => $zipInfo['absolute_path'],
                'size' => File::size($zipInfo['absolute_path']) . ' bytes'
            ]);

        } catch (Exception $e) {
            $zip->close();
            if (File::exists($zipInfo['absolute_path'])) {
                File::delete($zipInfo['absolute_path']);
            }
            throw $e;
        }
    }

    /**
     * Generate payslips ZIP for direct download (without saving to server)
     */
    public function generatePayslipsZipForDownload(array $data): array
    {
        try {
            $payrollData = $this->preparePayrollData($data);

            // Add employee_ids to payroll data if provided
            if (!empty($data['employee_ids'])) {
                $payrollData['employee_ids'] = $data['employee_ids'];
            }

            Log::info('Starting payslip ZIP generation for download', [
                'payroll_data' => $payrollData,
                'language' => $data['lang'] ?? 'en'
            ]);

            // Get payslip data
            $rawPayslips = $this->payslipRepository->getPayslips($payrollData);
            if ($rawPayslips->isEmpty()) {
                throw new Exception('No payslip data found');
            }

            $payslips = $this->processPayslipsData($rawPayslips);
            $company = $this->newCompanyRepository->getCompanyInfo($payrollData['company_id']);

            // Generate ZIP content in memory
            $language = $data['lang'] ?? 'en';
            $result = $this->generateZipInMemory($payslips, $company->name, $language, $payrollData);

            // Generate filename
            $fileName = sprintf(
                '%s_payslips_%02d_%d_%s.zip',
                preg_replace('/[^a-zA-Z0-9_-]/', '_', $company->code),
                $payrollData['month'],
                $payrollData['year'],
                date('Y-m-d_H-i-s')
            );

            Log::info('Payslip ZIP generation completed', [
                'total_employees' => $payslips->count(),
                'successfully_downloaded' => $result['successfully_downloaded'],
                'zip_size' => strlen($result['content']) . ' bytes',
                'filename' => $fileName
            ]);

            return [
                'content' => $result['content'],
                'filename' => $fileName,
                'total_employees' => $payslips->count(),
                'successfully_downloaded' => $result['successfully_downloaded'],
                'mime_type' => 'application/zip'
            ];

        } catch (Exception $e) {
            Log::error('Payslip ZIP generation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Get employees with payslip filters
     */
    public function getEmployees(array $data, array $filters = []): Collection
    {
        try {
            $payrollData = $this->preparePayrollData($data);

            Log::info('Getting employees with filters', [
                'payroll_data' => $payrollData,
                'filters' => $filters
            ]);

            return $this->payslipRepository->getEmployees($payrollData, $filters);

        } catch (Exception $e) {
            Log::error('Failed to get employees', [
                'error' => $e->getMessage(),
                'data' => $data,
                'filters' => $filters
            ]);
            throw $e;
        }
    }

    /**
     * List stored payslip files
     */
    public function listStoredFiles(int $companyId = null, int $year = null, int $month = null): array
    {
        try {
            $companyId = $companyId ?: $this->getCompanyId();
            $year = $year ?: date('Y');

            // Get company info for directory structure
            $company = $this->newCompanyRepository->getCompanyInfo($companyId);
            $companyCode = preg_replace('/[^a-zA-Z0-9_-]/', '_', $company->code);

            $basePath = storage_path("app/public/companies/{$companyCode}/payslips");

            if (!File::exists($basePath)) {
                return [];
            }

            $files = [];
            $pattern = $month ? sprintf('%02d_%d_payslips.zip', $month, $year) : "*_{$year}_payslips.zip";
            $searchPath = $basePath . '/' . $pattern;

            $foundFiles = glob($searchPath);

            foreach ($foundFiles as $filePath) {
                if (is_file($filePath)) {
                    $filename = basename($filePath);
                    $filesize = filesize($filePath);
                    $lastModified = filemtime($filePath);

                    // Extract month and year from filename
                    if (preg_match('/(\d{2})_(\d{4})_payslips\.zip/', $filename, $matches)) {
                        $fileMonth = (int) $matches[1];
                        $fileYear = (int) $matches[2];
                    } else {
                        continue; // Skip files that don't match expected format
                    }

                    // Create public URL
                    $relativePath = "companies/{$companyCode}/payslips/{$filename}";
                    $publicUrl = url("storage/{$relativePath}");

                    $files[] = [
                        'filename' => $filename,
                        'file_path' => $filePath,
                        'public_url' => $publicUrl,
                        'relative_path' => $relativePath,
                        'size' => $filesize,
                        'size_human' => $this->formatFileSize($filesize),
                        'last_modified' => $lastModified,
                        'last_modified_human' => date('Y-m-d H:i:s', $lastModified),
                        'month' => $fileMonth,
                        'year' => $fileYear,
                        'company_code' => $companyCode,
                        'company_id' => $companyId
                    ];
                }
            }

            // Sort by last modified (newest first)
            usort($files, function ($a, $b) {
                return $b['last_modified'] - $a['last_modified'];
            });

            Log::info('Listed stored payslip files', [
                'company_id' => $companyId,
                'year' => $year,
                'month' => $month,
                'files_found' => count($files),
                'base_path' => $basePath
            ]);

            return $files;

        } catch (Exception $e) {
            Log::error('Failed to list stored payslip files', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'year' => $year,
                'month' => $month
            ]);
            throw $e;
        }
    }

    /**
     * Format file size to human readable format
     */
    private function formatFileSize(int $size, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }

        return round($size, $precision) . ' ' . $units[$i];
    }

    /**
     * Generate ZIP content in memory without saving to disk
     */
    private function generateZipInMemory(Collection $payslips, string $companyName, string $language, array $payrollData): array
    {
        $tempZipPath = tempnam(sys_get_temp_dir(), 'payslips_') . '.zip';
        $successfullyDownloaded = [];

        try {
            $zip = new ZipArchive();
            $result = $zip->open($tempZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

            if ($result !== true) {
                throw new Exception("Cannot create temporary ZIP file. Error code: {$result}");
            }

            Log::info('Generating PDFs for ZIP', [
                'total_payslips' => $payslips->count(),
                'language' => $language
            ]);

            // Generate PDF for each employee and add to ZIP
            foreach ($payslips as $payslip) {
                try {
                    $pdf = $this->generateEmployeePDF($payslip, $companyName, $language);
                    $fileName = "payslip_{$payslip['employee']['code']}.pdf";
                    $pdfContent = $pdf->output();

                    $zip->addFromString($fileName, $pdfContent);

                    // Track successful generation
                    $successfullyDownloaded[] = $payslip['employee']['id'];

                    Log::debug("Added PDF to ZIP: {$fileName}", [
                        'employee_id' => $payslip['employee']['id'],
                        'pdf_size' => strlen($pdfContent) . ' bytes'
                    ]);

                } catch (Exception $e) {
                    Log::error("Failed to generate PDF for employee", [
                        'employee_id' => $payslip['employee']['id'] ?? 'unknown',
                        'employee_code' => $payslip['employee']['code'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other employees - ignore failed ones
                    continue;
                }
            }

            $zip->close();

            if (!file_exists($tempZipPath)) {
                throw new Exception('Temporary ZIP file was not created properly');
            }

            $zipContent = file_get_contents($tempZipPath);

            if ($zipContent === false) {
                throw new Exception('Failed to read temporary ZIP file content');
            }

            // Update download status in database for successful downloads only
            $this->updateSuccessfulDownloads($successfullyDownloaded, $payrollData);

            Log::info('ZIP generated successfully in memory', [
                'content_size' => strlen($zipContent) . ' bytes',
                'successfully_downloaded' => count($successfullyDownloaded)
            ]);

            return [
                'content' => $zipContent,
                'successfully_downloaded' => count($successfullyDownloaded)
            ];

        } finally {
            // Always clean up temporary file
            if (file_exists($tempZipPath)) {
                unlink($tempZipPath);
                Log::debug('Cleaned up temporary ZIP file');
            }
        }
    }

    /**
     * Generate PDF for single employee
     */
    private function generateEmployeePDF(array $payslip, string $companyName, string $language): \Barryvdh\DomPDF\PDF
    {
        $previousLocale = app()->getLocale();
        app()->setLocale($language);

        $langConfig = $this->config['supported_languages'][$language] ?? $this->config['supported_languages']['en'];

        $data = [
            'employee' => $payslip['employee'],
            'categories' => $payslip['categories'],
            'tax_amount' => $payslip['tax_amount'],
            'insurance_amount' => $payslip['insurance_amount'],
            'company_name' => $companyName,
            'direction' => $langConfig['direction'] ?? 'ltr',
            'language' => $language,
            'month' => $language === 'ar' ? $this->getArabicMonth(date('n')) : date('F'),
            'year' => date('Y')
        ];

        // PDF options
        Pdf::setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => true,
            'isRemoteEnabled' => false,
            'defaultFont' => 'DejaVu Sans',
            'defaultPaperSize' => 'A4',
            'defaultPaperOrientation' => 'portrait',
            'fontDir' => config('payslips.storage.fonts_path'),
            'fontCache' => config('payslips.storage.fonts_path'),
            'tempDir' => storage_path('app/temp/'),
            'isJavascriptEnabled' => false,
            'isFontSubsettingEnabled' => true,
        ]);

        $pdf = Pdf::loadView('admin.pdf-templates.payslip', $data);
        $pdf->setPaper('A4', 'portrait');

        app()->setLocale($previousLocale);

        return $pdf;
    }

    /**
     * Process raw payslip data
     */
    private function processPayslipsData(Collection $rawPayslips): Collection
    {
        return $rawPayslips->groupBy('employee_id')->map(function ($employeeComponents) {
            $firstRecord = $employeeComponents->first();

            $categories = $employeeComponents->groupBy('category_name')->map(function ($components) {
                $firstComponent = $components->first();
                $isAddition = $firstComponent->category_is_addition ?? true;

                return [
                    'is_addition' => $isAddition,
                    'components' => $components->map(function ($component) {
                        return [
                            'name' => $component->component_name,
                            'amount' => number_format($component->amount, 2)
                        ];
                    })->toArray()
                ];
            })->toArray();

            return [
                'employee' => [
                    'id' => $firstRecord->employee_id,
                    'name' => $firstRecord->employee_name,
                    'code' => $firstRecord->code,
                    'title' => $firstRecord->title,
                    'net_salary' => number_format($firstRecord->net_salary, 2)
                ],
                'categories' => $categories,
                'tax_amount' => number_format($firstRecord->tax_amount, 2),
                'insurance_amount' => number_format($firstRecord->insurance_amount, 2)
            ];
        })->values();
    }

    /**
     * Prepare payroll data from end_date parameter
     */
    private function preparePayrollData(array $data): array
    {
        $dateTo = \Carbon\Carbon::parse($data['end_date']);
        $month = $dateTo->month;
        $year = $dateTo->year;

        $companyId = $this->getCompanyId();

        $payrollId = $this->payrollsRepository->findPayrollId([
            'month' => $month,
            'year' => $year,
            'company_id' => $companyId
        ]);

        return [
            'month' => $month,
            'year' => $year,
            'payroll_id' => $payrollId,
            'company_id' => $companyId,
            'employee_ids' => isset($data['employee_ids']) ? $data['employee_ids'] : []
        ];
    }

    /**
     * Get the company ID with lazy loading and fallback
     */
    private function getCompanyId(): int
    {
        if ($this->companyId === null) {
            $this->companyId = config('globals.company')?->id ?? auth()->user()?->company_id;

            if (!$this->companyId) {
                throw new Exception('Company ID could not be resolved. Please ensure user is authenticated and company is configured.');
            }
        }

        return $this->companyId;
    }

    /**
     * Get Arabic month name
     */
    private function getArabicMonth(int $monthNumber): string
    {
        $arabicMonths = [
            1 => 'يناير',
            2 => 'فبراير',
            3 => 'مارس',
            4 => 'أبريل',
            5 => 'مايو',
            6 => 'يونيو',
            7 => 'يوليو',
            8 => 'أغسطس',
            9 => 'سبتمبر',
            10 => 'أكتوبر',
            11 => 'نوفمبر',
            12 => 'ديسمبر'
        ];

        return $arabicMonths[$monthNumber] ?? date('F');
    }

    /**
     * Update successful downloads using repository
     */
    private function updateSuccessfulDownloads(array $successfulEmployeeIds, array $payrollData): void
    {
        try {
            if (!empty($successfulEmployeeIds)) {
                $result = $this->payslipRepository->markEmployeesAsDownloaded($successfulEmployeeIds, $payrollData);

                if ($result) {
                    Log::info('Successfully updated download status', [
                        'employee_count' => count($successfulEmployeeIds),
                        'employee_ids' => $successfulEmployeeIds,
                        'payroll_id' => $payrollData['payroll_id']
                    ]);
                } else {
                    Log::warning('Download status update returned false', [
                        'employee_ids' => $successfulEmployeeIds,
                        'payroll_data' => $payrollData
                    ]);
                }
            }

        } catch (Exception $e) {
            Log::error('Failed to update successful downloads', [
                'error' => $e->getMessage(),
                'successful_employees' => $successfulEmployeeIds,
                'payroll_data' => $payrollData
            ]);
            // Don't throw exception here - the ZIP was still generated successfully
        }
    }
}
