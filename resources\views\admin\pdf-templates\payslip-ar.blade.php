<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ shapeArabic(__('salary_components.payslip')) }} -
        {{ isset($employee['name']) ? shapeArabic($employee['name']) : '' }}
    </title>
    <style>
        /* Base styles with better font support */
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            text-align: right;
            font-size: 14px;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }

        .container {
            width: 100%;
            margin: 0 auto;
        }

        /* Table-based layouts */
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: left;
        }

        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: #4285f4;
            text-align: right;
        }

        /* Line breaks */
        .line-break {
            border: none;
            border-top: 1px solid #333;
            margin: 15px 0;
        }

        .dashed-line {
            border: none;
            border-top: 1px dashed #ccc;
            margin: 8px 0;
        }

        /* Section One - Employee Info */
        .section-one {
            margin-bottom: 20px;
        }

        .payslip-month {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .employee-name {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .employee-code {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .employee-title {
            font-size: 16px;
            margin-bottom: 10px;
        }

        /* Section Two - Categories */
        .section-two {
            margin-bottom: 20px;
        }

        .category-header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .category-header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .category-name {
            font-weight: bold;
            font-size: 16px;
            text-align: right;
        }

        .amount-keyword {
            font-weight: bold;
            text-align: left;
        }

        .component-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .component-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .component-name {
            font-weight: bold;
            text-align: right;
        }

        .component-amount {
            font-weight: normal;
            text-align: left;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 5px;
            background-color: rgba(200, 200, 200, 0.8);
            margin-top: 8px;
        }

        .total-table td {
            padding: 8px 10px;
            font-weight: bold;
            vertical-align: middle;
        }

        .total-left {
            text-align: right;
        }

        .total-right {
            text-align: left;
        }

        /* Section Three - Taxes & Social Insurance */
        .section-three {
            margin-bottom: 20px;
        }

        .section-title-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .section-title-table td {
            padding: 0;
            vertical-align: middle;
        }

        .section-title-text {
            font-weight: bold;
            font-size: 16px;
            text-align: right;
        }

        .tax-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .tax-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .tax-left {
            text-align: right;
        }

        .tax-right {
            text-align: left;
        }

        /* Section Four - Net Pay */
        .section-four {
            margin-bottom: 20px;
        }

        .net-pay-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 5px;
            background-color: rgba(200, 200, 200, 0.8);
        }

        .net-pay-table td {
            padding: 12px 15px;
            font-weight: bold;
            font-size: 18px;
            vertical-align: middle;
        }

        .net-pay-left {
            text-align: right;
        }

        .net-pay-right {
            text-align: left;
        }

        /* Section Five - Signature */
        .section-five {
            margin-top: 40px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signature-table td {
            padding: 0;
            vertical-align: top;
        }

        .signature-container {
            text-align: right;
        }

        .signature-label {
            margin-top: 10px;
            margin-bottom: 20px;
            font-weight: normal;
        }

        .signature-box {
            border: 1px solid #333;
            width: 200px;
            height: 60px;
            margin: 0 0 0 auto;
        }

        .normal-text {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            unicode-bidi: bidi-override;
            text-align: right;
        }

        .number {
            font-family: 'DejaVu Sans', sans-serif !important;
            direction: ltr !important;
        }
    </style>
</head>

<body>
    @php
        // Global translation variables - declared once at the top
        $amountText = shapeArabic(__('salary_components.amount'));
        $currencyText = shapeArabic(__('salary_components.currency'));
        $totalText = shapeArabic(__('salary_components.total'));
    @endphp

    <div class="container">
        {{-- 1. Header: company_name, blueworks logo (justify-between) --}}
        <table class="header-table">
            <tr>
                {{-- Reversed order of columns --}}
                <td class="company-logo">blueworks.</td>
                <td class="company-name">
                    @php
                        $companyName = $company_name ?? 'Company Name';
                        $companyNameDisplay = shapeArabic($companyName);
                    @endphp
                    <span>
                        {{ $companyNameDisplay }}
                    </span>
                </td>
            </tr>
        </table>

        {{-- 2. Line break --}}
        <hr class="line-break">

        {{-- 3. Section One: Employee Info --}}
        <div class="section-one">
            {{-- Row one: payslip month --}}
            <div class="payslip-month">
                @php
                    $payslipForText = shapeArabic(__('salary_components.payslip_for'));
                @endphp
                <span class="normal-text">
                    {{ $year ?? '' }} {{ shapeArabic($month ?? '') }}
                </span>
                <span>
                    {{ $payslipForText }}
                </span>

            </div>

            {{-- Row two: employee name --}}
            <div class="employee-name">
                @php
                    $employeeNameText = shapeArabic(__('salary_components.employee_name'));
                    $employeeName = shapeArabic($employee['name'] ?? '');
                @endphp
                <span>
                    {{ $employeeName }}
                </span>
                <span>
                    :{{ $employeeNameText }}
                </span>

            </div>

            <div class="employee-code">
                @php
                    $codeText = shapeArabic(__('salary_components.employee_code'));
                @endphp
                <span class="number">{{ $employee['code'] ?? '' }}</span>
                <span>
                    :{{ $codeText }}
                </span>
            </div>

            <div class="employee-title">
                @php
                    $titleText = shapeArabic(__('salary_components.job_title'));
                    $employeeTitle = shapeArabic($employee['title'] ?? '');
                @endphp
                <span>
                    {{ $employeeTitle }}
                </span>
                <span>
                    :{{ $titleText }}
                </span>

            </div>
        </div>

        {{-- 4. Line break --}}
        <hr class="line-break">

        {{-- 5. Section Two: Categories (Iterative) --}}
        @if (isset($categories) && is_array($categories))
            @foreach ($categories as $categoryName => $categoryData)
                <div class="section-two">
                    {{-- Row one: category name (bold), amount_keyword (semi bold) --}}
                    <table class="category-header-table">
                        <tr>
                            {{-- Reversed order of columns --}}
                            <td class="amount-keyword">
                                <span>
                                    {{ $amountText }}
                                </span>
                            </td>
                            <td class="category-name">
                                @php
                                    $categoryKey = 'salary_components.' . trim($categoryName) ?? '';
                                    $translatedCategoryName = __($categoryKey) ?: $categoryName;
                                    $translatedCategoryName = shapeArabic($translatedCategoryName);
                                @endphp
                                <span>
                                    {{ $translatedCategoryName }}
                                </span>
                            </td>
                        </tr>
                    </table>

                    {{-- Dashed line break --}}
                    <div class="dashed-line"></div>

                    {{-- Row two (iterative): component name (bold), real amount --}}
                    @php $categoryTotal = 0; @endphp
                    @if (is_array($categoryData['components']))
                        @foreach ($categoryData['components'] as $component)
                            @php
                                $componentKey = 'salary_components.' . $component['name'] ?? '';
                                $translatedComponentName = __($componentKey) ?: $component['name'] ?? '';
                                $translatedComponentName = shapeArabic($translatedComponentName);
                                $amount = floatval(str_replace(',', '', $component['amount'] ?? '0'));
                                $categoryTotal += $amount;
                            @endphp
                            <table class="component-table">
                                <tr>
                                    {{-- Reversed order of columns --}}
                                    <td class="component-amount">
                                        <span class="number">
                                            <span>{{ $currencyText }}</span>
                                            {{ $categoryData['is_addition'] ? '' : '-' }}{{ $component['amount'] }}
                                        </span>
                                    </td>
                                    <td class="component-name">
                                        <span>
                                            {{ $translatedComponentName }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        @endforeach
                    @endif

                    {{-- Final row: total keyword, total amount (bold) --}}
                    <table class="total-table">
                        <tr>
                            {{-- Reversed order of columns --}}
                            <td class="total-right">
                                <span class="number">
                                    <span>{{ $currencyText }}</span>
                                    {{ $categoryData['is_addition'] ? '' : '-' }}{{ number_format($categoryTotal, 2) }}
                                </span>
                            </td>
                            <td class="total-left">
                                <span>
                                    {{ $totalText }} {{ $translatedCategoryName }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            @endforeach
        @endif

        {{-- 6. Section Three: Taxes & Social Insurance --}}
        <div class="section-three">
            {{-- Row one: title (taxes & social insurance) (bold), amount (semi bold) --}}
            <table class="section-title-table">
                <tr>
                    {{-- Reversed order of columns --}}
                    <td class="amount-keyword">
                        <span>
                            {{ $amountText }}
                        </span>
                    </td>
                    <td class="section-title-text">
                        @php
                            $taxInsuranceText = shapeArabic(__('salary_components.taxes_social_insurance'));
                        @endphp
                        <span>
                            {{ $taxInsuranceText }}
                        </span>
                    </td>
                </tr>
            </table>

            <div class="dashed-line"></div>

            {{-- Row two: taxes keyword, -amount --}}
            <table class="tax-table">
                <tr>
                    {{-- Reversed order of columns --}}
                    <td class="tax-right">
                        <span class="number">
                            <span>{{ $currencyText }}</span>
                            -{{ $tax_amount ?? '0' }}
                        </span>
                    </td>
                    <td class="tax-left">
                        @php
                            $taxesText = shapeArabic(__('salary_components.taxes'));
                        @endphp
                        <span>
                            {{ $taxesText }}
                        </span>
                    </td>
                </tr>
            </table>

            {{-- Row three: social insurance keyword, -amount --}}
            <table class="tax-table">
                <tr>
                    {{-- Reversed order of columns --}}
                    <td class="tax-right">
                        <span class="number">
                            <span>{{ $currencyText }}</span>
                            -{{ $insurance_amount ?? '0' }}
                        </span>
                    </td>
                    <td class="tax-left">
                        @php
                            $socialInsuranceText = shapeArabic(__('salary_components.social_insurance'));
                        @endphp
                        <span>
                            {{ $socialInsuranceText }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        {{-- 7. Section Four: Net Pay --}}
        <div class="section-four">
            <table class="net-pay-table">
                <tr>
                    {{-- Reversed order of columns --}}
                    <td class="net-pay-right">
                        <span class="number">
                            <span>{{ $currencyText }}</span>
                            {{ $net_pay ?? '0' }}
                        </span>
                    </td>
                    <td class="net-pay-left">
                        @php
                            $netPayText = shapeArabic(__('salary_components.net_pay'));
                        @endphp
                        <span>
                            {{ $netPayText }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        {{-- 8. Section Five: Employee Signature --}}
        <div class="section-five">
            <table class="signature-table">
                <tr>
                    {{-- Reversed order of columns --}}
                    <td class="signature-container">
                        <div class="signature-label">
                            @php
                                $signatureText = shapeArabic(__('salary_components.employee_signature'));
                            @endphp
                            <span>
                                :{{ $signatureText }}
                                <div class="signature-box"></div>
                            </span>
                        </div>
                    </td>
                    <td></td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>
