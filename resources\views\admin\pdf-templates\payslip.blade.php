<!DOCTYPE html>
<html dir="ltr" lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ shapeArabic(__('salary_components.payslip')) }} -
        {{ isset($employee['name']) ? shapeArabic($employee['name']) : '' }}
    </title>
    <style>
        /* Base styles with better font support */
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: ltr;
            text-align: left;
            font-size: 14px;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }

        .container {
            width: 100%;
            margin: 0 auto;
        }

        /* Table-based layouts */
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: left;
        }

        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: #4285f4;
            text-align: right;
        }

        /* Line break */
        .line-break {
            height: 1px;
            background-color: #ddd;
            margin: 15px 0;
            border: none;
        }

        /* Section One - Employee Info */
        .section-one {
            margin-bottom: 20px;
        }

        .payslip-month {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .employee-name {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .employee-code {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .employee-title {
            font-size: 16px;
            margin-bottom: 10px;
        }

        /* Section Two - Categories */
        .section-two {
            margin-bottom: 20px;
        }

        .category-header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .category-header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .category-name {
            font-weight: bold;
            font-size: 16px;
            text-align: left;
        }

        .amount-keyword {

            text-align: right;
            direction: rtl;
        }

        .dashed-line {
            border-top: 1px dashed #ccc;
            margin: 8px 0;
            height: 1px;
        }

        .component-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .component-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .component-name {
            font-weight: bold;
            text-align: left;
        }

        .component-amount {
            font-weight: normal;
            text-align: right;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 5px;
            background-color: rgba(200, 200, 200, 0.8);
            margin-top: 8px;
        }

        .total-table td {
            padding: 8px 10px;
            font-weight: bold;
            vertical-align: middle;
        }

        .total-left {
            text-align: left;
        }

        .total-right {
            text-align: right;
        }

        /* Section Three - Taxes & Social Insurance */
        .section-three {
            margin-bottom: 20px;
        }

        .section-title-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .section-title-table td {
            padding: 0;
            vertical-align: middle;
        }

        .section-title-text {
            font-weight: bold;
            font-size: 16px;
            text-align: left;
        }

        .tax-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .tax-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .tax-left {
            text-align: left;
        }

        .tax-right {
            text-align: right;
        }

        /* Section Four - Net Pay */
        .section-four {
            margin-bottom: 20px;
        }

        .net-pay-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 5px;
            background-color: rgba(200, 200, 200, 0.8);
        }

        .net-pay-table td {
            padding: 12px 15px;
            font-weight: bold;
            font-size: 18px;
            vertical-align: middle;
        }

        .net-pay-left {
            text-align: left;
        }

        .net-pay-right {
            text-align: right;
        }

        /* Section Five - Signature */
        .section-five {
            margin-top: 40px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signature-table td {
            padding: 0;
            vertical-align: top;
        }

        .signature-container {
            text-align: left;
        }

        .signature-label {
            margin-bottom: 10px;
            font-weight: normal;
        }

        .signature-box {
            border: 1px solid #333;
            width: 200px;
            height: 60px;
        }

        .normal-text {
            font-family: 'DejaVu Sans', sans-serif;
        }

        .number {
            font-family: 'DejaVu Sans', sans-serif !important;
            direction: ltr !important;
        }
    </style>
</head>

<body>
    @php
        // Global translation variables - declared once at the top
        $amountText = __('salary_components.amount');
        if (containsArabic($amountText)) {
            $amountText = shapeArabic($amountText);
        }

        $currencyText = __('salary_components.currency');
        if (containsArabic($currencyText)) {
            $currencyText = shapeArabic($currencyText);
        }

        $totalText = __('salary_components.total');
        if (containsArabic($totalText)) {
            $totalText = shapeArabic($totalText);
        }

        $fontClass = 'normal-text';
    @endphp

    <div class="container">
        {{-- 1. Header: company_name, blueworks logo (justify-between) --}}
        <table class="header-table">
            <tr>
                <td class="company-name">
                    @php
                        $companyName = $company_name ?? 'Company Name';
                        $companyNameDisplay = containsArabic($companyName) ? shapeArabic($companyName) : $companyName;
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $companyNameDisplay }}
                    </span>
                </td>
                <td class="company-logo">blueworks.</td>
            </tr>
        </table>

        {{-- 2. Line break --}}
        <hr class="line-break">

        {{-- 3. Section One: Employee Info --}}
        <div class="section-one">
            {{-- Row one: payslip month --}}
            <div class="payslip-month">
                @php
                    $payslipForText = __('salary_components.payslip_for');
                    if (containsArabic($payslipForText)) {
                        $payslipForText = shapeArabic($payslipForText);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $payslipForText }}
                </span>
                <span class="normal-text">{{ containsArabic($month ?? '') ? shapeArabic($month ?? '') : $month ?? '' }}
                    {{ $year ?? '' }}</span>
            </div>

            {{-- Row two: employee name --}}
            <div class="employee-name">
                @php
                    $employeeNameText = __('salary_components.employee_name');
                    if (containsArabic($employeeNameText)) {
                        $employeeNameText = shapeArabic($employeeNameText);
                    }

                    $employeeName = $employee['name'] ?? '';
                    if (containsArabic($employeeName)) {
                        $employeeName = shapeArabic($employeeName);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $employeeNameText }}:
                </span>
                <span class="{{ $fontClass }}">
                    {{ $employeeName }}
                </span>
            </div>

            {{-- Row three: code, title --}}
            <div class="employee-code">
                @php
                    $codeText = shapeArabic(__('salary_components.employee_code'));
                @endphp
                <span>{{ $codeText }}:</span>
                <span class="number">{{ $employee['code'] ?? '' }}</span>
            </div>

            <div class="employee-title">
                @php
                    $titleText = shapeArabic(__('salary_components.job_title'));
                    $employeeTitle = shapeArabic($employee['title'] ?? '');
                @endphp
                <span>{{ $titleText }}:</span>
                <span>{{ $employeeTitle }}</span>
            </div>
        </div>

        {{-- 4. Line break --}}
        <hr class="line-break">

        {{-- 5. Section Two: Categories (Iterative) --}}
        @if (isset($categories) && is_array($categories))
            @foreach ($categories as $categoryName => $categoryData)
                <div class="section-two">
                    {{-- Row one: category name (bold), amount_keyword (semi bold) --}}
                    <table class="category-header-table">
                        <tr>
                            <td class="category-name">
                                @php
                                    $categoryKey = 'salary_components.' . trim($categoryName) ?? '';
                                    $translatedCategoryName = __($categoryKey) ?: $categoryName;
                                    if (containsArabic($translatedCategoryName)) {
                                        $translatedCategoryName = shapeArabic($translatedCategoryName);
                                    }
                                @endphp
                                <span class="{{ $fontClass }}">
                                    {{ $translatedCategoryName }}
                                </span>
                            </td>
                            <td class="amount-keyword">
                                <span class="{{ $fontClass }}">
                                    {{ $amountText }}
                                </span>
                            </td>
                        </tr>
                    </table>

                    {{-- Dashed line break --}}
                    <div class="dashed-line"></div>

                    {{-- Row two (iterative): component name (bold), real amount --}}
                    @php $categoryTotal = 0; @endphp
                    @if (is_array($categoryData['components']))
                        @foreach ($categoryData['components'] as $component)
                            @php
                                $componentKey = 'salary_components.' . $component['name'] ?? '';
                                $translatedComponentName = __($componentKey) ?: $component['name'] ?? '';
                                if (containsArabic($translatedComponentName)) {
                                    $translatedComponentName = shapeArabic($translatedComponentName);
                                }
                                $amount = floatval(str_replace(',', '', $component['amount'] ?? '0'));
                                $categoryTotal += $amount;
                            @endphp
                            <table class="component-table">
                                <tr>
                                    <td class="component-name">
                                        <span class="{{ $fontClass }}">
                                            {{ $translatedComponentName }}
                                        </span>
                                    </td>
                                    <td class="component-amount">
                                        <span class="number">
                                            {{ $categoryData['is_addition'] ? '' : '-' }}{{ $component['amount'] }}
                                            <span class="{{ $fontClass }}">
                                                {{ $currencyText }}
                                            </span>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        @endforeach
                    @endif

                    {{-- Final row: total keyword, total amount (bold) --}}
                    <table class="total-table">
                        <tr>
                            <td class="total-left">
                                <span class="{{ $fontClass }}">
                                    {{ $totalText }} {{ $translatedCategoryName }}
                                </span>
                            </td>
                            <td class="total-right">
                                <span class="number">
                                    {{ number_format($categoryTotal, 2) }}
                                    <span class="{{ $fontClass }}">
                                        {{ $currencyText }}
                                    </span>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            @endforeach
        @endif

        {{-- 6. Section Three: Taxes & Social Insurance --}}
        <div class="section-three">
            {{-- Row one: title (taxes & social insurance) (bold), amount (semi bold) --}}
            <table class="section-title-table">
                <tr>
                    <td class="section-title-text">
                        @php
                            $taxInsuranceText = __('salary_components.taxes_social_insurance');
                            if (containsArabic($taxInsuranceText)) {
                                $taxInsuranceText = shapeArabic($taxInsuranceText);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $taxInsuranceText }}
                        </span>
                    </td>
                    <td class="amount-keyword">
                        <span class="{{ $fontClass }}">
                            {{ $amountText }}
                        </span>
                    </td>
                </tr>
            </table>

            <div class="dashed-line"></div>

            {{-- Row two: taxes keyword, -amount --}}
            <table class="tax-table">
                <tr>
                    <td class="tax-left">
                        @php
                            $taxesText = __('salary_components.taxes');
                            if (containsArabic($taxesText)) {
                                $taxesText = shapeArabic($taxesText);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $taxesText }}
                        </span>
                    </td>
                    <td class="tax-right">
                        <span class="number">
                            -{{ $tax_amount ?? '0' }}
                            <span class="{{ $fontClass }}">
                                {{ $currencyText }}
                            </span>
                        </span>
                    </td>
                </tr>
            </table>

            {{-- Row three: social insurance keyword, -amount --}}
            <table class="tax-table">
                <tr>
                    <td class="tax-left">
                        @php
                            $socialInsuranceText = __('salary_components.social_insurance');
                            if (containsArabic($socialInsuranceText)) {
                                $socialInsuranceText = shapeArabic($socialInsuranceText);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $socialInsuranceText }}
                        </span>
                    </td>
                    <td class="tax-right">
                        <span class="number">
                            -{{ $insurance_amount ?? '0' }}
                            <span class="{{ $fontClass }}">
                                {{ $currencyText }}
                            </span>
                        </span>
                    </td>
                </tr>
            </table>

            {{-- Final row: total keyword, total amount (bold) --}}
            <table class="total-table">
                <tr>
                    <td class="total-left">
                        @php
                            $totalTaxInsurance =
                                floatval(str_replace(',', '', $tax_amount ?? '0')) +
                                floatval(str_replace(',', '', $insurance_amount ?? '0'));
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $totalText }}
                        </span>
                    </td>
                    <td class="total-right">
                        <span class="number">
                            {{ number_format($totalTaxInsurance, 2) }}
                            <span class="{{ $fontClass }}">
                                {{ $currencyText }}
                            </span>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        {{-- 7. Section Four: Net Pay --}}
        <div class="section-four">
            <table class="net-pay-table">
                <tr>
                    <td class="net-pay-left">
                        @php
                            $netPayText = __('salary_components.net_pay');
                            if (containsArabic($netPayText)) {
                                $netPayText = shapeArabic($netPayText);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $netPayText }}
                        </span>
                    </td>
                    <td class="net-pay-right">
                        <span class="number">
                            {{ $employee['net_salary'] ?? '0' }}
                            <span class="{{ $fontClass }}">
                                {{ $currencyText }}
                            </span>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        {{-- 8. Section Five: Employee Signature --}}
        <div class="section-five">
            <table class="signature-table">
                <tr>
                    <td></td>
                    <td class="signature-container">
                        <div class="signature-label">
                            @php
                                $signatureText = __('salary_components.employee_signature');
                                if (containsArabic($signatureText)) {
                                    $signatureText = shapeArabic($signatureText);
                                }
                            @endphp
                            <span class="{{ $fontClass }}">
                                {{ $signatureText }}:
                            </span>
                        </div>
                        <div class="signature-box"></div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>